# GitHub Copilot Instructions

This file provides guidance for AI coding agents working with the BCMI laboratory website rebuild project.

## Project Overview

This is a Next.js 15 project rebuilding the BCMI (Brain-like Computing & Machine Intelligence) laboratory website with modern frameworks and Payload CMS for content management. The project follows a dual-architecture pattern with separate frontend and CMS route groups.

## Architecture & Key Patterns

### Route Group Architecture
- `src/app/(frontend)/` - Public website with layout.tsx and globals.css
- `src/app/(payload)/` - CMS admin interface with separate layout and custom.scss
- **Critical**: Route groups enable different layouts - frontend uses global layout components, CMS has isolated admin interface

### Layout System Pattern
The project uses a sophisticated responsive layout system centered around `MainLayout`:

```tsx
// Homepage: No sidebar
<MainLayout>
  
// Content pages: Conditional sidebar
<MainLayout sidebar="research">
```

**Layout Components Hierarchy:**
- `SiteHeader` → Logo, navigation (uses Payload Nav global), mobile drawer
- `MainLayout` → Conditional sidebar wrapper, responsive grid (lg:grid-cols-4)
- `SidebarNavigation` → Category-based nav, desktop sticky/mobile collapsible
- `SiteFooter` → Contact info, links

### Sidebar Configuration Pattern
Sidebars are pre-configured in `sidebarConfigs` object in `sidebar-navigation.tsx`:
```tsx
sidebar="research" // Activates research sidebar config
```
Each config defines title and navigation items - modify this object to add new categories.

### Payload CMS Integration
- Configuration: `src/payload.config.ts` with empty collections array (needs population)
- Global Nav: `src/globals/Nav.ts` drives site navigation
- Data Sources: Reference `data/original-content/*.json` for content structure
- **Admin Access**: `/admin` route, GraphQL at `/api/graphql`

## Tech Stack & Dependencies

- **Frontend**: Next.js 15 with React 19, TypeScript, Turbopack dev server
- **CMS**: Payload CMS v3.49 with PostgreSQL adapter and Lexical rich text editor
- **Styling**: Tailwind CSS v4 with complete shadcn/ui component library pre-installed
- **Key Dependencies**: All Radix UI primitives, class-variance-authority, cmdk, lucide-react

## Development Workflow & Commands

**Essential Commands:**
- `pnpm dev` - Development server with Turbopack (faster than webpack)
- `pnpm build` - Production build (MUST pass before completing tasks)
- `pnpm lint` - ESLint validation (MUST be clean before completing tasks)

**Critical Workflow:**
1. **Quality Gates**: ALL TypeScript errors and ESLint warnings must be resolved
2. **Build Validation**: Production build must succeed without errors
3. **Import Cleanup**: Remove unused imports/variables consistently
4. **Pattern Consistency**: Follow existing component patterns and file structure

## Project-Specific Conventions

### Component Development Patterns
- **shadcn/ui First**: Use existing components before creating new ones
- **Clean Exports**: Export components through index files (`src/components/layout/index.ts`)
- **TypeScript Interfaces**: Define props and data structures with explicit types
- **Utility Classes**: Use `cn()` from `@/lib/utils` for conditional class merging

### File Organization Conventions  
- **Layout Components**: Global components in `src/components/layout/`
- **UI Components**: Complete shadcn/ui library in `src/components/ui/`
- **Content Data**: Reference structure in `data/original-content/` for content modeling
- **Route Separation**: Frontend routes in `(frontend)`, CMS admin in `(payload)`

### Responsive Design Patterns
- **Desktop**: Sidebar navigation sticky positioning, 4-column grid layout
- **Mobile**: Collapsible sidebar sections, full-width main content
- **Breakpoint**: Primary responsive breakpoint at `lg:` (1024px)

## Integration Points & Data Flow

### Content Management Flow
1. **Static Fallbacks**: Default navigation items in `site-header.tsx` for initial deployment
2. **CMS Integration**: Navigation driven by Payload Nav global when available
3. **Content Migration**: Original content structure in `data/original-content/` guides CMS collection design

### Database & Environment Setup
- **Database**: PostgreSQL connection via `DATABASE_URI` environment variable
- **Security**: `PAYLOAD_SECRET` environment variable required
- **Assets**: Sharp image processing configured for media handling

This project prioritizes build reliability, responsive design patterns, and clean separation between public frontend and CMS admin interfaces.

## Current Website Analysis

The existing BCMI website features:
- Homepage with hero carousel and laboratory introduction
- Faculty Members section with profile cards and research interests
- Research areas and publications
- Events section (upcoming conferences, past events)
- Resources and forum sections
- Bilingual support (English/Chinese)
- Academic focus on brain-like computing, machine learning, computer vision

## Environment Variables

Required environment variables:
- `PAYLOAD_SECRET` - Payload CMS secret key
- `DATABASE_URI` - PostgreSQL connection string

## Development Notes

- Payload collections need to be defined based on content types from existing website
- shadcn/ui components are fully installed and ready to use
- The project structure separates frontend and CMS concerns clearly
- Next.js config is wrapped with Payload's withPayload() function