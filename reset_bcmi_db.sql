-- reset_bcmi_db.sql

-- 断开连接
SELECT pg_terminate_backend(pid)
FROM pg_stat_activity
WHERE datname = 'bcmi' AND pid <> pg_backend_pid();

-- 删除数据库
DROP DATABASE IF EXISTS bcmi;

-- 删除用户（如果需要重新创建）
DROP USER IF EXISTS bcmi_user;

-- 创建用户
CREATE USER bcmi_user WITH PASSWORD 'your_secure_password_here';

-- 创建数据库
CREATE DATABASE bcmi 
    WITH OWNER = bcmi_user
    ENCODING = 'UTF8'
    LC_COLLATE = 'en_US.UTF-8'
    LC_CTYPE = 'en_US.UTF-8'
    TEMPLATE = template0;

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE bcmi TO bcmi_user;

-- 连接到新数据库并设置权限
\c bcmi postgres

GRANT ALL ON SCHEMA public TO bcmi_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO bcmi_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO bcmi_user;

-- 显示结果
\l bcmi
