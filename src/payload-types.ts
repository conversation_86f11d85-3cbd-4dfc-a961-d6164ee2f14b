/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    events: Event;
    media: Media;
    members: Member;
    positions: Position;
    'research-areas': ResearchArea;
    'research-demos': ResearchDemo;
    resources: Resource;
    users: User;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    events: EventsSelect<false> | EventsSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    members: MembersSelect<false> | MembersSelect<true>;
    positions: PositionsSelect<false> | PositionsSelect<true>;
    'research-areas': ResearchAreasSelect<false> | ResearchAreasSelect<true>;
    'research-demos': ResearchDemosSelect<false> | ResearchDemosSelect<true>;
    resources: ResourcesSelect<false> | ResourcesSelect<true>;
    users: UsersSelect<false> | UsersSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {
    nav: Nav;
  };
  globalsSelect: {
    nav: NavSelect<false> | NavSelect<true>;
  };
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "events".
 */
export interface Event {
  id: number;
  title: string;
  /**
   * URL-friendly identifier for this event (auto-generated from title if not provided)
   */
  slug: string;
  /**
   * Detailed description of the event
   */
  description?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  /**
   * Brief summary for event listing cards (optional, will use truncated description if not provided)
   */
  summary?: string | null;
  eventType: 'conference' | 'workshop' | 'seminar' | 'symposium' | 'lecture' | 'competition' | 'meeting' | 'other';
  status: 'upcoming' | 'ongoing' | 'completed' | 'cancelled';
  startDate: string;
  /**
   * Leave empty for single-day events
   */
  endDate?: string | null;
  /**
   * Timezone for the event (e.g., "Asia/Shanghai", "UTC")
   */
  timezone?: string | null;
  location?: {
    /**
     * Name of the venue, building, or platform
     */
    venue?: string | null;
    /**
     * Physical address or online meeting details
     */
    address?: string | null;
    /**
     * Specific room, hall, or meeting ID
     */
    room?: string | null;
    isOnline?: boolean | null;
  };
  /**
   * Organization or person organizing the event
   */
  organizer?: string | null;
  speakers?:
    | {
        name: string;
        affiliation?: string | null;
        bio?: string | null;
        photo?: (number | null) | Media;
        id?: string | null;
      }[]
    | null;
  /**
   * Tags for categorizing and filtering events
   */
  tags?:
    | {
        tag: string;
        id?: string | null;
      }[]
    | null;
  /**
   * Main image for the event (used in listings and detail page)
   */
  featuredImage?: (number | null) | Media;
  gallery?:
    | {
        image: number | Media;
        caption?: string | null;
        id?: string | null;
      }[]
    | null;
  externalLinks?:
    | {
        title: string;
        url: string;
        type?: ('registration' | 'website' | 'program' | 'materials' | 'recording' | 'other') | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Order for sorting events (lower numbers first)
   */
  displayOrder?: number | null;
  /**
   * Whether this event is visible on the website
   */
  isPublished?: boolean | null;
  /**
   * Featured events are highlighted on the homepage and events page
   */
  isFeatured?: boolean | null;
  /**
   * For completed events - how many people attended
   */
  attendeeCount?: number | null;
  /**
   * For completed events - summary of outcomes, achievements, or key takeaways
   */
  outcomes?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: number;
  /**
   * Alternative text for screen readers and SEO
   */
  alt?: string | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    card?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    demo?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "members".
 */
export interface Member {
  id: number;
  name: string;
  /**
   * Professional headshot or profile photo
   */
  photo?: (number | null) | Media;
  email?: string | null;
  /**
   * Which position category this member belongs to
   */
  position: number | Position;
  /**
   * Faculty member who mentors this person (leave empty for faculty)
   */
  mentor?: (number | null) | Member;
  /**
   * Order within position/mentor group (lower numbers first)
   */
  displayOrder?: number | null;
  /**
   * e.g., "Professor", "Associate Professor", "Lecturer"
   */
  title?: string | null;
  /**
   * Department or affiliation
   */
  department?: string | null;
  /**
   * Brief description of research areas and interests
   */
  researchInterests?: string | null;
  /**
   * Detailed biographical information
   */
  bio?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  officeLocation?: string | null;
  phone?: string | null;
  /**
   * URL to personal or academic homepage
   */
  personalWebsite?: string | null;
  /**
   * Educational background and degrees
   */
  education?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  /**
   * Whether this member is currently with the lab (uncheck for alumni)
   */
  isActive?: boolean | null;
  /**
   * When this member joined the lab
   */
  joinDate?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "positions".
 */
export interface Position {
  id: number;
  /**
   * Display name for this position (e.g., "Faculty Members", "PhD Students")
   */
  title: string;
  /**
   * URL-friendly identifier (auto-generated from title)
   */
  slug: string;
  /**
   * Optional description for this position category
   */
  description?: string | null;
  /**
   * Order in which this position appears in the sidebar (lower numbers first)
   */
  displayOrder: number;
  /**
   * Whether this position should be shown in the members sidebar
   */
  isVisible?: boolean | null;
  /**
   * Whether members in this position should be grouped by their mentor
   */
  showMentorGrouping?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "research-areas".
 */
export interface ResearchArea {
  id: number;
  title: string;
  /**
   * Used for URL anchors (e.g., "cv", "bci"). Must be unique and URL-friendly.
   */
  anchor: string;
  /**
   * Main description text for this research area
   */
  description?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  bulletPoints?:
    | {
        point: string;
        id?: string | null;
      }[]
    | null;
  /**
   * Order in which this area appears in the sidebar and page
   */
  order: number;
  isVisible?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "research-demos".
 */
export interface ResearchDemo {
  id: number;
  title: string;
  /**
   * Optional description of the demo
   */
  description?: string | null;
  /**
   * Optional image to showcase the demo
   */
  image?: (number | null) | Media;
  /**
   * Link to the demo detail page or external demo
   */
  demoUrl: string;
  /**
   * Check if this link goes to an external website
   */
  isExternal?: boolean | null;
  /**
   * Which research area this demo belongs to
   */
  researchArea: number | ResearchArea;
  /**
   * Order in which this demo appears within its research area
   */
  order?: number | null;
  /**
   * Featured research demos are highlighted in the homepage carousel
   */
  isFeatured?: boolean | null;
  /**
   * High-quality image for homepage carousel (recommended: 1200x600px)
   */
  carouselImage?: (number | null) | Media;
  /**
   * Optional custom title for carousel display (uses demo title if not provided)
   */
  carouselTitle?: string | null;
  /**
   * Short description for carousel display (uses demo description if not provided)
   */
  carouselDescription?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "resources".
 */
export interface Resource {
  id: number;
  title: string;
  /**
   * URL-friendly identifier for this resource (auto-generated from title if not provided)
   */
  slug: string;
  /**
   * Full description of the resource
   */
  description?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  /**
   * Brief summary for resource cards (optional, will use truncated description if not provided)
   */
  summary?: string | null;
  resourceType: 'dataset' | 'software' | 'course' | 'tutorial' | 'documentation' | 'publication' | 'code' | 'other';
  /**
   * Category for organizing resources (e.g., "Machine Learning", "EEG Data", "Computer Vision")
   */
  category?: string | null;
  /**
   * Tags for search and filtering
   */
  tags?:
    | {
        tag: string;
        id?: string | null;
      }[]
    | null;
  /**
   * Information for downloadable resources
   */
  downloadInfo?: {
    hasDownload?: boolean | null;
    /**
     * Upload the actual file for download
     */
    file?: (number | null) | Media;
    /**
     * URL to external download (use this if file is hosted elsewhere)
     */
    downloadUrl?: string | null;
    /**
     * Human-readable file size (e.g., "1.2MB", "10.8GB")
     */
    fileSize?: string | null;
    /**
     * File format/extension (e.g., "ZIP", "RAR", "CSV", "JSON")
     */
    format?: string | null;
    /**
     * Any system requirements or dependencies
     */
    requirements?: string | null;
  };
  /**
   * Information for courses and tutorials
   */
  courseInfo?: {
    isCourse?: boolean | null;
    instructor?: string | null;
    /**
     * Course duration (e.g., "8 weeks", "20 hours")
     */
    duration?: string | null;
    difficulty?: ('beginner' | 'intermediate' | 'advanced') | null;
    /**
     * Required knowledge or skills
     */
    prerequisites?: string | null;
    /**
     * Detailed course content or syllabus
     */
    syllabus?: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    } | null;
  };
  /**
   * Information for research publications and papers
   */
  publicationInfo?: {
    isPublication?: boolean | null;
    authors?: string | null;
    journal?: string | null;
    year?: number | null;
    /**
     * Digital Object Identifier
     */
    doi?: string | null;
    abstract?: {
      root: {
        type: string;
        children: {
          type: string;
          version: number;
          [k: string]: unknown;
        }[];
        direction: ('ltr' | 'rtl') | null;
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
        indent: number;
        version: number;
      };
      [k: string]: unknown;
    } | null;
  };
  links?:
    | {
        title: string;
        url: string;
        type?: ('website' | 'github' | 'docs' | 'demo' | 'download' | 'publication' | 'video' | 'other') | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Main image for the resource (used in listings and detail view)
   */
  featuredImage?: (number | null) | Media;
  gallery?:
    | {
        image: number | Media;
        caption?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Order for sorting resources (lower numbers first)
   */
  displayOrder?: number | null;
  /**
   * Whether this resource is visible on the website
   */
  isPublished?: boolean | null;
  /**
   * Featured resources are highlighted on the homepage and resources page
   */
  isFeatured?: boolean | null;
  /**
   * Who can access this resource
   */
  accessLevel?: ('public' | 'members' | 'restricted') | null;
  /**
   * Number of times this resource has been downloaded (auto-updated)
   */
  downloadCount?: number | null;
  /**
   * Number of times this resource has been viewed (auto-updated)
   */
  viewCount?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  sessions?:
    | {
        id: string;
        createdAt?: string | null;
        expiresAt: string;
      }[]
    | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'events';
        value: number | Event;
      } | null)
    | ({
        relationTo: 'media';
        value: number | Media;
      } | null)
    | ({
        relationTo: 'members';
        value: number | Member;
      } | null)
    | ({
        relationTo: 'positions';
        value: number | Position;
      } | null)
    | ({
        relationTo: 'research-areas';
        value: number | ResearchArea;
      } | null)
    | ({
        relationTo: 'research-demos';
        value: number | ResearchDemo;
      } | null)
    | ({
        relationTo: 'resources';
        value: number | Resource;
      } | null)
    | ({
        relationTo: 'users';
        value: number | User;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "events_select".
 */
export interface EventsSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  description?: T;
  summary?: T;
  eventType?: T;
  status?: T;
  startDate?: T;
  endDate?: T;
  timezone?: T;
  location?:
    | T
    | {
        venue?: T;
        address?: T;
        room?: T;
        isOnline?: T;
      };
  organizer?: T;
  speakers?:
    | T
    | {
        name?: T;
        affiliation?: T;
        bio?: T;
        photo?: T;
        id?: T;
      };
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  featuredImage?: T;
  gallery?:
    | T
    | {
        image?: T;
        caption?: T;
        id?: T;
      };
  externalLinks?:
    | T
    | {
        title?: T;
        url?: T;
        type?: T;
        id?: T;
      };
  displayOrder?: T;
  isPublished?: T;
  isFeatured?: T;
  attendeeCount?: T;
  outcomes?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        thumbnail?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        card?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        demo?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "members_select".
 */
export interface MembersSelect<T extends boolean = true> {
  name?: T;
  photo?: T;
  email?: T;
  position?: T;
  mentor?: T;
  displayOrder?: T;
  title?: T;
  department?: T;
  researchInterests?: T;
  bio?: T;
  officeLocation?: T;
  phone?: T;
  personalWebsite?: T;
  education?: T;
  isActive?: T;
  joinDate?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "positions_select".
 */
export interface PositionsSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  description?: T;
  displayOrder?: T;
  isVisible?: T;
  showMentorGrouping?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "research-areas_select".
 */
export interface ResearchAreasSelect<T extends boolean = true> {
  title?: T;
  anchor?: T;
  description?: T;
  bulletPoints?:
    | T
    | {
        point?: T;
        id?: T;
      };
  order?: T;
  isVisible?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "research-demos_select".
 */
export interface ResearchDemosSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  image?: T;
  demoUrl?: T;
  isExternal?: T;
  researchArea?: T;
  order?: T;
  isFeatured?: T;
  carouselImage?: T;
  carouselTitle?: T;
  carouselDescription?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "resources_select".
 */
export interface ResourcesSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  description?: T;
  summary?: T;
  resourceType?: T;
  category?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  downloadInfo?:
    | T
    | {
        hasDownload?: T;
        file?: T;
        downloadUrl?: T;
        fileSize?: T;
        format?: T;
        requirements?: T;
      };
  courseInfo?:
    | T
    | {
        isCourse?: T;
        instructor?: T;
        duration?: T;
        difficulty?: T;
        prerequisites?: T;
        syllabus?: T;
      };
  publicationInfo?:
    | T
    | {
        isPublication?: T;
        authors?: T;
        journal?: T;
        year?: T;
        doi?: T;
        abstract?: T;
      };
  links?:
    | T
    | {
        title?: T;
        url?: T;
        type?: T;
        id?: T;
      };
  featuredImage?: T;
  gallery?:
    | T
    | {
        image?: T;
        caption?: T;
        id?: T;
      };
  displayOrder?: T;
  isPublished?: T;
  isFeatured?: T;
  accessLevel?: T;
  downloadCount?: T;
  viewCount?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
  sessions?:
    | T
    | {
        id?: T;
        createdAt?: T;
        expiresAt?: T;
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "nav".
 */
export interface Nav {
  id: number;
  items: {
    label: string;
    href: string;
    newTab?: boolean | null;
    id?: string | null;
  }[];
  updatedAt?: string | null;
  createdAt?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "nav_select".
 */
export interface NavSelect<T extends boolean = true> {
  items?:
    | T
    | {
        label?: T;
        href?: T;
        newTab?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  globalType?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}