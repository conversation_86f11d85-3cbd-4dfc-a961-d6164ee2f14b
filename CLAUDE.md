# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Next.js 15 project aimed at rebuilding the BCMI (Brain-like Computing & Machine Intelligence) laboratory website at https://bcmi.sjtu.edu.cn/ with modern frameworks and an easy-to-use admin panel. The project uses Payload CMS as the headless CMS backend.

## Tech Stack

- **Frontend**: Next.js 15 with React 19, TypeScript
- **CMS**: Payload CMS v3.49 with PostgreSQL database
- **Styling**: Tailwind CSS v4 with shadcn/ui components
- **Rich Text**: Lexical editor via @payloadcms/richtext-lexical
- **Development**: Turbopack for faster development builds
- **UI Components**: Complete shadcn/ui component library pre-installed

## Development Commands

- `pnpm dev` - Start development server with Turbopack
- `pnpm build` - Build for production
- `pnpm start` - Start production server
- `pnpm lint` - Run ESLint
- `pnpm generate:types` - Generate TypeScript types from Payload collections and globals

## Development Best Practices

**Required Development Workflow:**

1. **Plan & Discuss** - Create TodoWrite list to track implementation steps
2. **Implement** - Write code following existing patterns and conventions
3. **Generate Types** - Run `pnpm generate:types` after modifying collections or globals
4. **Validate** - Run `pnpm lint` to check for code quality issues
5. **Test Build** - Run `pnpm build` to ensure production build succeeds
6. **Update Documentation** - Update CLAUDE.md with new components/patterns

**Code Quality Standards:**
- Fix ALL TypeScript errors before completing tasks
- Fix ALL ESLint warnings before completing tasks
- Ensure production build passes without errors
- Remove unused imports and variables
- Follow existing component patterns and file structure

**Component Development:**
- Use existing shadcn/ui components when possible
- Follow the established layout pattern (SiteHeader, MainLayout, SiteFooter)
- Create reusable components in appropriate directories
- Export components through index files for clean imports
- Use TypeScript interfaces for all props and data structures

**Testing Checklist:**
- [ ] `pnpm lint` passes with no errors/warnings
- [ ] `pnpm build` completes successfully
- [ ] All pages render without console errors
- [ ] Responsive behavior works on mobile/desktop
- [ ] Navigation links function correctly

## Architecture

### Directory Structure

- `src/app/(frontend)/` - Public-facing website pages
- `src/app/(payload)/` - Payload CMS admin interface and API routes
- `src/components/ui/` - Complete shadcn/ui component library
- `src/components/layout/` - Global layout components (SiteHeader, SiteFooter, MainLayout, SidebarNavigation)
- `src/lib/utils.ts` - Utility functions including cn() for class merging
- `src/payload.config.ts` - Payload CMS configuration

### Layout Components

**Global Layout System:**
- `SiteHeader` - Logo, navigation menu, responsive mobile drawer
- `SiteFooter` - Contact info, quick links, copyright
- `MainLayout` - Conditional sidebar wrapper with responsive behavior
- `SidebarNavigation` - Category-based navigation for content pages

**Layout Patterns:**
- Homepage: Full-width layout without sidebar
- Content pages (Research, Members, Events, Resources): Sidebar + main content
- Responsive: Desktop sidebar → Mobile collapsible sections

### Route Groups

The app uses Next.js route groups:
- `(frontend)` - Main website routes (layout.tsx, globals.css)
- `(payload)` - CMS admin routes with separate layout

### Payload CMS Setup

- Configuration in `src/payload.config.ts`
- Uses PostgreSQL adapter with connection via `DATABASE_URI` env var
- Lexical rich text editor configured
- Collections: Media, Members, Positions, ResearchAreas, ResearchDemos
- Globals: Nav
- Admin interface available at `/admin`
- GraphQL API at `/api/graphql`
- **Local API Integration**: Uses Payload's local API for Next.js SSR with full TypeScript support
- **Type Generation**: Run `pnpm generate:types` after modifying collections/globals to update `src/payload-types.ts`

### Styling

- Tailwind CSS v4 with @tailwindcss/postcss plugin
- Custom theme configuration in `src/app/(frontend)/globals.css`
- Uses CSS custom properties for theming (light/dark mode supported)
- Geist font family (sans and mono) from Google Fonts

## Current Website Analysis

The existing BCMI website features:
- Homepage with hero carousel and laboratory introduction
- Faculty Members section with profile cards and research interests
- Research areas and publications
- Events section (upcoming conferences, past events)
- Resources and forum sections
- Bilingual support (English/Chinese)
- Academic focus on brain-like computing, machine learning, computer vision

## Environment Variables

Required environment variables:
- `PAYLOAD_SECRET` - Payload CMS secret key
- `DATABASE_URI` - PostgreSQL connection string

## Development Notes

- Payload collections need to be defined based on content types from existing website
- shadcn/ui components are fully installed and ready to use
- The project structure separates frontend and CMS concerns clearly
- Next.js config is wrapped with Payload's withPayload() function