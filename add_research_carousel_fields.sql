-- Add carousel fields to research_demos table
-- This script adds the new fields for homepage carousel functionality

-- Add the new columns to research_demos table
ALTER TABLE research_demos 
ADD COLUMN IF NOT EXISTS is_featured BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS carousel_image_id INTEGER REFERENCES media(id),
ADD COLUMN IF NOT EXISTS carousel_title TEXT,
ADD COLUMN IF NOT EXISTS carousel_description TEXT;

-- Create index for better performance when querying featured research
CREATE INDEX IF NOT EXISTS research_demos_is_featured_idx ON research_demos(is_featured);

-- Optional: Mark some existing research demos as featured for testing
-- UPDATE research_demos SET is_featured = TRUE WHERE id IN (1, 2, 3);

COMMIT;